import { useQuery } from "@connectrpc/connect-query";
import {
  Await,
  createFileRoute,
  notFound,
  useNavigate,
} from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";

import {
  vtuberBannerClient,
  vtuberProfilesClient,
} from "@vtuber/services/client";
import { VtuberPlanService } from "@vtuber/services/vtubers";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Button } from "@vtuber/ui/components/button";
import { Card, CardHeader, CardTitle } from "@vtuber/ui/components/card";

import { Container } from "@vtuber/ui/components/container";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { RouteFallback } from "@vtuber/ui/components/route-fallback";
import { Separator } from "@vtuber/ui/components/separator";
import { Skeleton } from "@vtuber/ui/components/skeleton";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { Tooltip } from "@vtuber/ui/components/tooltip";
import { Lock } from "lucide-react";
import { Suspense, useState } from "react";
import { z } from "zod";
import { CallToAction } from "~/components/layout/call-to-action";
import { MembershipContent } from "~/components/member-ship-content";
import { MembershipModal } from "~/components/membership-modal";
import { RecommendedPosts } from "~/components/post/recommended-posts";
import { PostCardSkeleton } from "~/components/skeletons/post-card-skeleton";
import { RecommendedPostsSkeleton } from "~/components/skeletons/recommended-posts-skeleton";
import { TopVtuberParticipatingEventsSkeleton } from "~/components/skeletons/top-vtuber-participating-events-skeleton";
import { VtuberCampaignsSkeleton } from "~/components/skeletons/vtuber-campaigns-skeleton";
import { VtuberEventSkeleton } from "~/components/skeletons/vtuber-events-skeleton";
import { VtuberGallerySkeleton } from "~/components/skeletons/vtuber-gallery-skeleton";
import { VtuberParticipatingEventsSkeleton } from "~/components/skeletons/vtuber-participating-events-skeleton";
import { PostList } from "~/components/vtuber/post-lists";
import { ToggleFavVtuber } from "~/components/vtuber/toggle-fav-vtuber";
import { TopVtuberParticipatingEvents } from "~/components/vtuber/top-vtuber-participating-events";
import { VtuberBanner } from "~/components/vtuber/vtuber-banner";
import { VtuberBanners } from "~/components/vtuber/vtuber-banners";
import { VtuberCampaigns } from "~/components/vtuber/vtuber-campaigns";
import { VtuberEventsList } from "~/components/vtuber/vtuber-events-list";
import { VtuberGallery } from "~/components/vtuber/vtuber-gallery";
import { VtuberParticipatingEvents } from "~/components/vtuber/vtuber-participating-events";
import { VtuberPostFitlers } from "~/components/vtuber/vtuber-post-filters";
import { VtuberProfile } from "~/components/vtuber/vtuber-profile";
import { validatePagination } from "~/data/constants";
import {
  recommendedPostsQueryOptions,
  vtuberCampaignsQueryOptions,
  vtuberEventsQueryOptions,
  vtuberGalleryQueryOptions,
  vtuberParticipatingEventsQueryOptions,
  vtuberPostQueryOptions,
} from "~/utils/api";

export const Route = createFileRoute("/_app/vtuber/$id/")({
  component: RouteComponent,
  validateSearch: validatePagination.extend({
    query: z.string().optional(),
    tab: z.string().optional(),
  }),
  loader: async ({ params, context }) => {
    const [vtuber, err] = await vtuberProfilesClient.getVtuberProfileById({
      id: params.id,
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    const bannersPromise = vtuberBannerClient.getVtuberBannerByVtuberId({
      vtuberId: params.id,
    });

    await context.queryClient.prefetchQuery(
      vtuberParticipatingEventsQueryOptions({
        vtuberId: vtuber?.data?.id!,
        size: 5,
        sort: "created_at",
        order: "desc",
      }),
    );
    await context.queryClient.prefetchQuery(
      recommendedPostsQueryOptions({
        size: 5,
        // vtuberId: params.id,
      }),
    );

    await context.queryClient.prefetchQuery(
      vtuberGalleryQueryOptions({
        vtuberId: vtuber?.data?.id!,
        enabled: !!vtuber?.data?.id,
        size: 9,
      }),
    );

    await context.queryClient.prefetchInfiniteQuery(
      vtuberPostQueryOptions({
        vtuberUsername: params.id,
        size: 3,
      }),
    );

    await context.queryClient.prefetchQuery(
      vtuberEventsQueryOptions({
        vtuberId: vtuber?.data?.id!,
        size: 3,
      }),
    );

    await context.queryClient.prefetchQuery(
      vtuberCampaignsQueryOptions({
        vtuberId: vtuber?.data?.id!,
        size: 3,
        enabled: !!vtuber?.data?.id,
      }),
    );

    return {
      vtuber,
      bannersPromise,
    };
  },
});

function RouteComponent() {
  const { tab } = Route.useSearch();
  const [tabValue, setTabValue] = useState(tab || "posts");

  const { id } = Route.useParams();
  const navigate = useNavigate({ from: Route.fullPath });
  const { vtuber, bannersPromise } = Route.useLoaderData();
  const { getText } = useLanguage();

  const { data, isPending, refetch } = useQuery(
    VtuberPlanService.method.getAllVtuberPlansByVtuberId,
    {
      vtuberId: vtuber?.data?.id!,
    },
    {
      enabled: vtuber.data?.isPlanActive && tabValue === "membership",
    },
  );

  const onTabChange = (v: string) => {
    setTabValue(v);
    navigate({
      search: {
        tab: v,
      },
      resetScroll: false,
    });
  };

  if (!vtuber?.data)
    return (
      <div className="min-h-[90dvh] pt-20 flex items-center justify-center">
        <RouteFallback data={{ data: getText("vtuber_not_found") }} />
      </div>
    );
  return (
    <div>
      <VtuberBanner vtuber={vtuber.data} />
      <Container className="-translate-y-10">
        <Suspense fallback={<TopVtuberParticipatingEventsSkeleton />}>
          <TopVtuberParticipatingEvents vtuberId={vtuber.data?.id!} />
        </Suspense>
      </Container>
      <div className="space-y-24 pt-20">
        <Container className="grid gap-y-[72px]">
          <div className="grid md:grid-cols-2 grid-cols-1 md:gap-x-12">
            <div className="space-y-8">
              <VtuberProfile vtuber={vtuber.data} />
              <section className="flex sm:items-center items-start sm:gap-y-0 gap-y-4 sm:gap-x-8 sm:flex-row flex-col">
                <ToggleFavVtuber vtuber={vtuber?.data} />
                {vtuber.data.isPlanActive ? (
                  <MembershipModal
                    vtuberId={vtuber?.data.id!}
                    asChild>
                    <Button
                      variant={"outline"}
                      size={"xl"}
                      className={"rounded-full font-medium md:px-8 px-4"}>
                      {vtuber.data.isUserSubscribed
                        ? getText("upgrade")
                        : getText("become_a_member")}
                    </Button>
                  </MembershipModal>
                ) : (
                  <Tooltip label={getText("subscription_not_active")}>
                    <div className="rounded-full border-font border py-3 flex items-center gap-2 font-medium px-4 md:px-8 opacity-60 cursor-not-allowed ">
                      <Lock className="size-4" />
                      <p className="line-clamp-1 text-ellipsis max-w-56">
                        {getText("subscription_not_active")}
                      </p>
                    </div>
                  </Tooltip>
                )}
              </section>
              <MarkDown
                markdown={vtuber.data?.description}
                className="text-font font-medium"
              />
              <ExternalLinks socialMediaLinks={vtuber.data?.socialMediaLinks} />
            </div>

            <Await
              promise={bannersPromise}
              fallback={
                <AspectRatio
                  ratio={525 / 350}
                  className="rounded-lg overflow-hidden"
                  style={{
                    filter: "drop-shadow(0px 0px 21px #EBC888)",
                  }}>
                  <Skeleton className="size-full rounded-lg" />
                </AspectRatio>
              }
              children={([res, err]) =>
                !err && <VtuberBanners banners={res?.data} />
              }
            />
          </div>
          <Tabs
            variant={"underlined"}
            value={tabValue}
            onValueChange={onTabChange}>
            <TabsList className="w-full h-[60px] relative">
              <div className="absolute inset-0">
                <div className="absolute inset-0 bg-background z-20" />
                <div
                  className="absolute h-full bg-background z-10 -translate-x-1/2 left-1/2 w-[80%]"
                  style={{
                    filter: "drop-shadow(0px 10px 30px rgba(0,0,0,0.45))",
                  }}
                />
              </div>
              <div className="grid size-full grid-cols-5 w-[80%] mx-auto relative z-20">
                <TabsTrigger value="posts">{getText("whats_new")}</TabsTrigger>
                <TabsTrigger value="event_lists">
                  {getText("event_list")}
                </TabsTrigger>
                <TabsTrigger
                  value="crowdfunding"
                  className="capitalize">
                  {getText("crowdfunding")}
                </TabsTrigger>
                <TabsTrigger value="gallery">
                  {getText("movies_and_galleries")}
                </TabsTrigger>
                <TabsTrigger value="membership">
                  {getText("membership")}
                </TabsTrigger>
              </div>
            </TabsList>
            <TabsContent value="posts">
              <div className="grid grid-cols-12 gap-x-24 items-start pt-[72px] px-16">
                <section className="space-y-8 col-span-8">
                  <h3 className="text-h2-pc font-bold leading-normal text-center">
                    {getText("whats_new")}
                  </h3>
                  <VtuberPostFitlers />
                  <Suspense
                    fallback={
                      <div className="sm:space-y-10 space-y-8">
                        <PostCardSkeleton />
                        <PostCardSkeleton />
                        <PostCardSkeleton />
                      </div>
                    }>
                    <PostList
                      isPlanActive={vtuber.data.isPlanActive}
                      vtuberId={vtuber.data.id}
                      id={id}
                      hasSubscribed={vtuber.data.isUserSubscribed}
                    />
                  </Suspense>
                </section>
                <section className="col-span-4 sticky top-24">
                  <Card className="py-10 px-6 rounded-10 space-y-8">
                    <CardHeader className="p-0 space-y-[14px]">
                      <CardTitle className="font-bold text-xl text-font capitalize">
                        {getText("recommended_posts")}
                      </CardTitle>
                      <Separator className="bg-blue01 h-[2px]" />
                    </CardHeader>
                    <Suspense fallback={<RecommendedPostsSkeleton />}>
                      <RecommendedPosts vtuber={vtuber.data} />
                    </Suspense>
                  </Card>
                </section>
              </div>
            </TabsContent>
            <TabsContent value="event_lists">
              <div className="grid grid-cols-12 gap-x-24 items-start pt-[72px] px-16">
                <section className="flex flex-col gap-y-8 col-span-8">
                  <h3 className="text-h2-pc font-bold leading-normal text-center">
                    {getText("event_list")}
                  </h3>
                  <Suspense fallback={<VtuberParticipatingEventsSkeleton />}>
                    <VtuberParticipatingEvents vtuberId={vtuber.data?.id!} />
                  </Suspense>
                </section>
                <section className="col-span-4 sticky top-24">
                  <Card className="py-10 px-6 rounded-10 space-y-8">
                    <CardHeader className="p-0 space-y-[14px]">
                      <CardTitle className="font-bold text-xl text-font capitalize">
                        {getText("latest_events")}
                      </CardTitle>
                      <Separator className="bg-blue01 h-[2px]" />
                    </CardHeader>
                    <Suspense fallback={<VtuberEventSkeleton />}>
                      <VtuberEventsList vtuberId={vtuber.data?.id!} />
                    </Suspense>
                  </Card>
                </section>
              </div>
            </TabsContent>
            <TabsContent value="crowdfunding">
              <div className="pt-[72px] w-[80%] mx-auto">
                <Suspense fallback={<VtuberCampaignsSkeleton />}>
                  <VtuberCampaigns vtuberId={vtuber.data?.id} />
                </Suspense>
              </div>
            </TabsContent>
            <TabsContent value="gallery">
              <div className="pt-[72px] w-[80%] mx-auto space-y-14">
                <h3 className="text-h2-pc font-bold leading-normal text-center">
                  {getText("movies_and_galleries")}
                </h3>
                <Suspense fallback={<VtuberGallerySkeleton />}>
                  <VtuberGallery vtuberId={vtuber.data.id} />
                </Suspense>
              </div>
            </TabsContent>
            <TabsContent value="membership">
              <div className="pt-[72px] w-[80%] mx-auto space-y-14 ">
                {vtuber.data.isPlanActive ? (
                  <MembershipContent
                    conditionClasses="min-h-52"
                    isPending={isPending}
                    data={data?.VtuberPlan}
                    refetch={refetch}
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center min-h-[300px] space-y-4">
                    <div className="size-20 bg-gradient-2 rounded-full flex items-center justify-center">
                      <Lock className="size-12" />
                    </div>
                    <div className="text-center space-y-2">
                      <h4 className="font-semibold text-2xl text-font">
                        {getText("subscription_not_active")}
                      </h4>
                      <div className="text-sm text-muted-foreground">
                        <p>
                          {vtuber.data.displayName}{" "}
                          {getText("not_accepting_subcriptions")}
                        </p>
                        <p>{getText("please_check_back_later")}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </Container>
        <CallToAction />
      </div>
    </div>
  );
}
