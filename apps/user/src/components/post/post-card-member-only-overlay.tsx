import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { LockIcon } from "@vtuber/ui/components/icons/lock-icon";
import { toast } from "sonner";
import { MembershipModal } from "../membership-modal";

interface Props {
  vtuberId: bigint;
  isPlanActive: boolean;
}

export const PostCardMemberOnlyOverlay = ({
  vtuberId,
  isPlanActive,
}: Props) => {
  const { getText } = useLanguage();
  return (
    <div className="inset-0 absolute z-20 bg-[hsl(221, 27%, 26%, 10%)] flex items-center justify-center backdrop-blur-sm">
      <div className="flex flex-col items-center gap-y-3">
        <p className="font-bold">{getText("membership_only_content")}</p>
        {isPlanActive ? (
          <MembershipModal
            vtuberId={vtuberId}
            asChild>
            <Button className="bg-gradient-2 font-bold gap-x-3 h-12 px-8 text-white">
              <LockIcon className="mr-2" />
              {getText("view_paid_post")}
            </Button>
          </MembershipModal>
        ) : (
          <Button
            onClick={() => {
              toast.info(getText("vtuber_not_accepting_subscription_message"));
            }}
            className="bg-gradient-2 font-bold gap-x-3 h-12 px-8 text-white">
            <LockIcon className="mr-2" />
            {getText("view_paid_post")}
          </Button>
        )}
      </div>
    </div>
  );
};
