import { useNavigate, useSearch } from "@tanstack/react-router";
import { GetCampaignById } from "@vtuber/services/campaigns";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { useScrollInToView } from "@vtuber/ui/hooks/use-scroll-intoview";
import { useEffect, useState } from "react";

import { useLanguage } from "@vtuber/language/hooks";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { PostList } from "../vtuber/post-lists";
import { CampaignSupporters } from "./campaign-supporters";

type Props = {
  campaign: GetCampaignById;
};

export const CampaignOverviewTabs = ({ campaign }: Props) => {
  const { getText } = useLanguage();
  const { commentId, tab } = useSearch({ from: "/_app/campaign/$id" });
  const [tabValue, setTabValue] = useState(tab || "introduction");
  const navigate = useNavigate();
  const { ref } = useScrollInToView({ trigger: commentId });

  useEffect(() => {
    if (tab) {
      setTabValue(tab);
    }
  }, [tab]);

  const hasSubscribed = campaign.variants.some((v) => v.hasSubscribed);

  return (
    <div ref={ref}>
      <Tabs
        variant={"outline"}
        value={tabValue}
        onValueChange={(val) => {
          setTabValue(val);
          navigate({
            // @ts-ignore
            search: (prev) => ({
              ...prev,
              tab: val,
            }),
            resetScroll: false,
          });
        }}>
        <TabsList className="md:justify-center justify-start w-full md:flex-nowrap flex-wrap gap-2">
          <TabsTrigger
            value="introduction"
            className="sm:text-xl text-base sm:min-w-max flex-1">
            {getText("project_introduction")}
          </TabsTrigger>
          <TabsTrigger
            value="activity"
            className="sm:text-xl text-base sm:min-w-max flex-1">
            {getText("activity_report")}
          </TabsTrigger>
          <TabsTrigger
            value="supporters"
            className="sm:text-xl text-base sm:min-w-max flex-1">
            {getText("supporters")}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="introduction">
          <article className="pt-10 grid gap-y-10">
            <DisplayTag text="project_introduction" />
            <MarkDown
              markdown={campaign?.description}
              className="text-font"
            />
          </article>
        </TabsContent>
        <TabsContent value="activity">
          <div className="pt-10 grid gap-y-10">
            <DisplayTag text="activity_report" />
            <PostList
              vtuberId={campaign?.vtuber?.id!}
              campaignId={campaign.id}
              hasSubscribed={hasSubscribed}
              isPlanActive={campaign.vtuber?.isPlanActive!}
            />
          </div>
        </TabsContent>
        <TabsContent value="supporters">
          <div className="pt-10 grid gap-y-10">
            <DisplayTag text="supporters" />
            <CampaignSupporters
              campaignId={campaign.id}
              tabValue={tabValue}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
