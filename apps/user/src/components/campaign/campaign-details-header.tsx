import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useCategories } from "@vtuber/auth/hooks";
import { GetCampaignById } from "@vtuber/services/campaigns";
import { Avatar } from "@vtuber/ui/components/avatar";
import { RemainingDays } from "@vtuber/ui/components/remaining-days";
import { Tag } from "@vtuber/ui/components/tag";
import { ToggleFavCampaign } from "./toggle-fav-campaign";

export const CampaignDetailsHeader = ({
  campaign,
}: {
  campaign: GetCampaignById;
}) => {
  const { getMultipleCategories } = useCategories();

  const tags = getMultipleCategories(campaign.categories);

  return (
    <header className="gap-y-6 grid">
      <h1 className="sm:text-[26px] text-2xl font-bold text-font">
        {campaign.name}
      </h1>
      <section className="flex sm:items-center sm:flex-row flex-col items-start sm:gap-y-0 gap-y-6 sm:justify-between">
        <div className="flex items-center gap-x-5">
          <Avatar
            className="size-11"
            alt={campaign.vtuber?.displayName}
            src={campaign.vtuber?.image}
            fallback={campaign.vtuber?.displayName}
          />
          <p className="text-[#777777] text-xl font-medium">
            {campaign.vtuber?.displayName}
          </p>
        </div>
        <ToggleFavCampaign campaign={campaign} />
      </section>
      <section className="flex md:items-end flex-wrap gap-x-5">
        <p className="font-medium sm:text-base text-sm text-font">
          {timestampDate(campaign.startDate!).toLocaleDateString()} -{" "}
          {timestampDate(campaign.endDate!).toLocaleDateString()}
        </p>
        {/* {!!campaign?. && (
          <Tag
            variant={"success"}
            className="sm:text-base text-xs">
            {getCategoryById(campaign.categoryId)?.name}
          </Tag>
        )} */}
        <RemainingDays
          className="text-[17px]"
          daysClassname="text-[34px]"
          endDate={campaign.endDate!}
          startDate={campaign.startDate!}
        />
      </section>
      <section className="flex items-center md:gap-5 gap-2 flex-wrap">
        {tags.length > 0 &&
          tags.map((t) => (
            <Tag
              className="h-8 px-6"
              variant={"outline-white"}
              key={t?.id}>
              {t?.name}
            </Tag>
          ))}
      </section>
    </header>
  );
};
