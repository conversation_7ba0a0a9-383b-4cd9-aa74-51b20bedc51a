import { timestampDate } from "@bufbuild/protobuf/wkt";
import { Link } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { Event } from "@vtuber/services/events";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { Tag } from "@vtuber/ui/components/tag";
import { cn, GetProtoType } from "@vtuber/ui/lib/utils";

interface Props {
  event: GetProtoType<Event>;
  className?: string;
}

export const EventCard = ({ event, className }: Props) => {
  const { getMultipleCategories } = useCategories();
  const tags = getMultipleCategories(event.categories);
  return (
    <Link
      to="/event/$id"
      params={{
        id: event.slug,
      }}>
      <Card className="bg-transparent border-none shadow-none">
        <AspectRatio ratio={571 / 321}>
          <Image
            className={cn("rounded-[20px] border border-white", className)}
            src={event.image}
            alt={event.title}
          />
        </AspectRatio>
        <CardContent className="p-0 space-y-[14px] pt-3">
          <p className="text-[#F7F5F5] text-sm font-medium">
            {
              timestampDate(event.startDate!)
                .toISOString()
                .replaceAll("-", "/")
                .split("T")[0]
            }{" "}
            -{" "}
            {
              timestampDate(event.endDate!)
                .toISOString()
                .replaceAll("-", "/")
                .split("T")[0]
            }{" "}
          </p>

          <h3 className="font-bold text-[22px]">{event.title}</h3>
          <p className="text-[#E2E2E2] text-sm">{event.shortDescription}</p>
          {tags.length > 0 && (
            <div className="flex items-center gap-3 flex-wrap">
              {tags.map((t) => (
                <Tag
                  key={t?.id}
                  variant={"outline-white"}
                  className="h-8 px-6">
                  {t?.name}
                </Tag>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  );
};
