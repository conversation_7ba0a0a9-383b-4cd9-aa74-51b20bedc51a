import { Link } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { Campaign } from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { buttonVariants } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { Image } from "@vtuber/ui/components/image";
import { Progress } from "@vtuber/ui/components/progress";
import { RemainingDays } from "@vtuber/ui/components/remaining-days";
import { DottedWithRectangleShape } from "@vtuber/ui/components/shape/dotted-with-rectangle";
import { Tag } from "@vtuber/ui/components/tag";
import { cn, getProgress } from "@vtuber/ui/lib/utils";
import { ArrowRight } from "lucide-react";

export const PopularCampaign = ({ campaign }: { campaign: Campaign }) => {
  const { getText } = useLanguage();
  const { getMultipleCategories } = useCategories();

  const progress = getProgress(campaign.totalBudget, campaign.totalRaised);

  const tags = getMultipleCategories(campaign.categories);

  return (
    <section className="md:pt-32 pt-16 md:pb-44 pb-28 overflow-hidden flex flex-col items-center justify-center">
      <div className="relative flex-col flex items-center justify-center md:py-36 py-20">
        <div
          className="md:w-[3096px] w-[2582px] absolute h-full bg-sub rounded-[100%] left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2"
          style={{
            filter: "drop-shadow(0px 0px 29px #9376CD)",
          }}
        />
        <DottedWithRectangleShape className="absolute right-6 -top-10 md:h-[179px] md:w-[157px] w-[100px] h-[115px]" />
        <Container className="grid md:gap-y-28 gap-y-10 relative">
          <div className="flex flex-col gap-y-[88px]">
            <ContentHeading
              title="popular_campaign"
              subTitle="Popular Crowdfunding"
              className="text-balance"
            />
            <div className="grid grid-cols-12 items-center md:gap-x-10 md:gap-y-0 gap-y-10">
              <section className="md:col-span-5 col-span-12 md:order-1 order-2 flex flex-col md:gap-y-[14px] gap-y-[22px] items-start">
                <h3 className="font-bold md:text-[26px] text-[22px] text-font">
                  {campaign?.name}
                </h3>
                <div className="flex flex-col gap-y-[14px] w-full">
                  {tags?.length > 0 && (
                    <div className="flex items-center flex-wrap gap-3">
                      {tags.map((t) => (
                        <Tag
                          key={t?.id}
                          variant={"outline"}
                          className="rounded-xs text-white border-white w-max">
                          {t?.name}
                        </Tag>
                      ))}
                    </div>
                  )}
                  <div className="flex items-center justify-between w-full">
                    <h3 className="font-bold md:text-[54px] text-[39px] text-font">
                      {campaign?.totalBudget?.toLocaleString()}{" "}
                      <span className="md:text-[31px] text-2xl">
                        {getText("yen")}
                      </span>
                    </h3>
                    <RemainingDays
                      endDate={campaign.endDate!}
                      startDate={campaign.startDate!}
                    />
                  </div>
                  <div className="w-full">
                    <div className="flex items-center justify-between text-font md:text-base text-xs">
                      <small>{getText("progress")}</small>
                      <small>{progress}％</small>
                    </div>
                    <Progress value={progress} />
                  </div>
                </div>
              </section>
              <section
                className="md:col-span-7 col-span-12 md:order-2 order-1 overflow-hidden"
                style={{
                  filter: "drop-shadow(0px 0px 21px #EBC888)",
                }}>
                <AspectRatio
                  ratio={699 / 393}
                  className="border border-font rounded-10">
                  <Image
                    src={campaign?.thumbnail || ""}
                    className="rounded-10"
                    alt={campaign?.name}
                  />
                </AspectRatio>
              </section>
            </div>
          </div>
          <div className="flex justify-center">
            <Link
              to="/campaign/$id"
              params={{
                id: campaign?.slug || "",
              }}
              className={cn(
                buttonVariants({
                  variant: "outline",
                  size: "xl",
                }),
                "flex items-center gap-x-8 rounded-full",
              )}>
              {getText("see_details")} <ArrowRight />
            </Link>
          </div>
        </Container>
      </div>
    </section>
  );
};
